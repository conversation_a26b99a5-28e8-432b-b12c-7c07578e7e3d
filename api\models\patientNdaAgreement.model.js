const history = require("./plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const PatientNdaAgreement = sequelize.define(
    "PatientNdaAgreement",
    {
      patient_nda_agreement_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      patient_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      signed_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      signer_role: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      nda_template_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "nda_template",
          key: "nda_template_id",
        },
        onDelete: "CASCADE",
      },
      effective_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      expiration_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "nda_agreement",
      timestamps: true,
      underscored: true,
    }
  );

  PatientNdaAgreement.associate = (models) => {
    PatientNdaAgreement.belongsTo(models.NdaTemplate, {
      foreignKey: "nda_template_id",
      as: "template",
    });

    PatientNdaAgreement.belongsTo(models.Identity, {
      foreignKey: "identity_id",
      as: "identity",
    });

    PatientNdaAgreement.belongsTo(models.MasterData, {
      foreignKey: "status",
      targetKey: "key",
      as: "nda_agreement_status_name",
      constraints: false,
      scope: {
        group: "nda_agreement_status",
      },
    });
    PatientNdaAgreement.belongsTo(models.MasterData, {
      foreignKey: "signer_role",
      targetKey: "key",
      as: "signer_role_name",
      constraints: false,
      scope: {
        group: "signer_role",
      },
    });

    PatientNdaAgreement.hasMany(models.PatientNdaSignature, {
      foreignKey: "patient_nda_agreement_id",
      as: "signatures",
    });
  };

  history(PatientNdaAgreement, sequelize, DataTypes);

  return PatientNdaAgreement;
};
