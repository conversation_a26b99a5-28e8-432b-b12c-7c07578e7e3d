import React, { useState, useRef,useEffect } from "react";
import { useNavigate } from 'react-router-dom';
import DateInput from "../../Components/Global/Input/ValidationHubDate";
import GenericTable from "../../Components/GenericTable";
import DetailsCard from "../../Components/Global/DetailsCard";
import PrintModal from "../../Components/Global/PrintModal";
import EditPhotoModal from "../../Components/Global/ImageAndCamera/EditPhotoModal";
import HostSearch from "./HostSearch";
import VisitorSearch from "./VisitorSearch";
import useClickOutside from "../InpatientVisit/useClickOutside";
import Button from "../../Components/Global/Button";
import VisitorForm from "../../Components/Global/Forms/VisitorForm";
import defaultImg from "../../Images/demoimg.svg"
import moment from "moment";
import { FilterButtons } from "../../Components/GenericTable";
// import { VisitorsDoctorsData } from "../../api/static"
import { getVisitorColumns } from "../../api/tableDataColumns"; 
import homeicon from "../../Images/home-icon.svg";
import CreateVisitorModal from "../../Components/Global/CreateVisitorModal"; // Import the modal for creating a new visitor
import { searchIdentities } from "../../api/global"; // <-- import the API
import formatDateTime from "../../utils/formatDate";
import { getVisitGuests } from "../../api/visitor-hub";
import { searchGuests } from "../../api/guest";

const ReceptionDesk = () => {
  // ---------------- State Variables ----------------
  const [searchTerm, setSearchTerm] = useState("");
  const [hostSearchTerm, setHostSearchTerm] = useState("");
  const [tableSearchTerm, setTableSearchTerm] = useState("");
  const navigate = useNavigate();
  const [searchResults, setSearchResults] = useState([]);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [guestSearchTerm, setGuestSearchTerm] = useState("");
  const [guestSearchResults, setGuestSearchResults] = useState([]);
  const [isGuestDropdownVisible, setIsGuestDropdownVisible] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState(null);
  // selectedGuest state is declared below, so do not redeclare here

  // State for the complete list of visitors (across all hosts)
  const [allGuests, setAllGuests] = useState([]);
  const [selectedGuest, setSelectedGuest] = useState(null); // Track selected guest for DetailsCard
  // Set default test values for facilityId and visitId to trigger API call
  const [facilityId, setFacilityId] = useState(""); // Replace with real value or from props/context
  const [visitId, setVisitId] = useState(""); // Replace with real value or from props/context

  // Fetch guests from API
  useEffect(() => {
    const fetFacilityId = localStorage.getItem("selectedFacility");

    if (fetFacilityId) {
      getVisitGuests(fetFacilityId)
        .then((response) => {
          const guests = response?.data?.data || [];
          setAllGuests(guests);
          setDateFilteredGuests(guests);
        })
        .catch(() => {
          setAllGuests([]);
          setDateFilteredGuests([]);
        });
    }
  }, []);

  // We'll derive our final list based on the selected date and filter button.
  const [dateFilteredGuests, setDateFilteredGuests] = useState([]);
  const [activeFilter, setActiveFilter] = useState("all");
  const [printModalVisible, setPrintModalVisible] = useState(false);
  // selectedGuest state is declared above
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedGuestId, setSelectedGuestId] = useState(null);
  const [showVisitorForm, setShowVisitorForm] = useState(false);
  const [patientSearchPlaceholder, setPatientSearchPlaceholder] = useState("Search By Host Name, EID");
  const [guestSearchPlaceholder, setGuestSearchPlaceholder] = useState("Search By Guest");

  // New state for custom date picker
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [isCreateVisitorModalOpen, setIsCreateVisitorModalOpen] = useState(false); // State for the create visitor modal

  // Sorting state
  const [sortBy, setSortBy] = useState("");
  const [sortOrder, setSortOrder] = useState("ASC");

  // Refs for clickOutside logic
  const patientSearchRef = useRef(null);
  const guestSearchRef = useRef(null);
  useClickOutside(guestSearchRef, () => setIsGuestDropdownVisible(false));
  useClickOutside(patientSearchRef, () => setIsDropdownVisible(false));

  // ---------------- Handlers ----------------
  const handleHome = () => {
    setSelectedPatient(null);
    setPatientSearchPlaceholder("Search By Host Name, EID");
    setGuestSearchPlaceholder("Search By Guest");

    // Re-fetch all visitors from API if needed
    if (facilityId && visitId) {
      getVisitGuests(facilityId, visitId)
        .then((guests) => {
          setAllGuests(guests);
          setDateFilteredGuests(guests);
        })
        .catch(() => {
          setAllGuests([]);
          setDateFilteredGuests([]);
        });
    }
  };

  const handleHostSearchInputChange = async (value) => {
    setSearchTerm(value);
    if (value.trim()) {
      // Use the API to search for hosts
      try {
        const apiResults = await searchIdentities(value);
        console.log("API response from /master-data/identity-hub:", apiResults);
        const results = apiResults?.data?.data || [];
        console.log("Processed search results:", results);
        setSearchResults(results);
        setIsDropdownVisible(true);
      } catch (error) {
        console.error("Error searching identities:", error);
        setSearchResults([]);
        setIsDropdownVisible(false);
      }
    } else {
      setSearchResults([]);
      setIsDropdownVisible(false);
    }
  };

  const handleHostClick = (host) => {
    console.log("Selected host from /master-data/identity-hub:", host);
    setSelectedPatient(host);
    // Set facilityId and visitId from host, which triggers useEffect to fetch guests
    setFacilityId(host.facilityId || host.site || host.facility_id || null);
    setVisitId(host.visitId || host.id || null);
    setIsDropdownVisible(false);
    setSearchTerm("");
    setPatientSearchPlaceholder(host.name || `${host.first_name || ''} ${host.last_name || ''}`.trim());
    setShowVisitorForm(false);
  };

  const handleVisitorSearchInputChange = async (value) => {
    setGuestSearchTerm(value);
    if (value.trim()) {
      // Try all possible fields for search
      const params = {
        first_name: value,
        last_name: value,
        email: value,
        phone: value,
      };
      try {
        const apiResults = await searchGuests(params);
        setGuestSearchResults(apiResults.data || []);
        setIsGuestDropdownVisible(true);
      } catch (error) {
        setGuestSearchResults([]);
        setIsGuestDropdownVisible(false);
      }
    } else {
      setGuestSearchResults([]);
      setIsGuestDropdownVisible(false);
    }
  };

  const handleGuestClick = (visitor) => {
    // Set selected guest for DetailsCard
    setSelectedGuest(visitor);
    setIsGuestDropdownVisible(false);
    setGuestSearchResults([]);
    setGuestSearchTerm("");
    setGuestSearchPlaceholder(visitor.visitorName);
    setShowVisitorForm(false);
  };

  const handleCreateVisitorClick = () => {
    setIsCreateVisitorModalOpen(true); // Open the create visitor modal
  };

  const handleCreateVisitorSubmit = (newVisitor) => {
    // Add the new visitor to the list
    if (selectedPatient) {
      newVisitor.hostName = selectedPatient.name;
      newVisitor.facility = selectedPatient.site;
    }
    const updatedAllGuests = [newVisitor, ...allGuests];
    setAllGuests(updatedAllGuests);
    setDateFilteredGuests(updatedAllGuests);
    setIsCreateVisitorModalOpen(false); // Close the modal
  };

  const handleImageCaptured = (imageData) => {
    const updatedGuests = dateFilteredGuests.map((guest) =>
      guest.id === selectedGuestId ? { ...guest, image: imageData } : guest
    );
    setAllGuests(updatedGuests);
    setDateFilteredGuests(updatedGuests);
    setIsModalOpen(false);
  };

  const openModal = (title, guestId) => {
    setSelectedGuestId(guestId);
    setIsModalOpen(true);
  };

  const handlePrintClick = (guest) => {
    setSelectedGuest(guest);
    setPrintModalVisible(true);
  };

  const handleClosePrintModal = () => {
    setPrintModalVisible(false);
    setSelectedGuest(null);
  };

  const handleAddVisitor = (newVisitor) => {
    if (selectedPatient) {
      newVisitor.hostName = selectedPatient.name;
      newVisitor.facility = selectedPatient.site;
    }
    const updatedAllGuests = [newVisitor, ...allGuests];
    setAllGuests(updatedAllGuests);
    setDateFilteredGuests(updatedAllGuests);
    console.log("Visitor Added:", newVisitor);
  };

  const handleHistoryOpen = () => {
    console.log("History panel opened");
  };

  // Handle sorting functionality
  const handleSort = (column, sortDirection) => {
    console.log('ReceptionDesk - Sort clicked:', { column, sortDirection });

    // Extract column identifier from column object
    const columnId = column.id || column.selector || column.name;
    console.log('Sorting by column:', columnId, 'Direction:', sortDirection);

    setSortBy(columnId);
    setSortOrder(sortDirection.toUpperCase());

    // Apply sorting to the current data
    const sortedData = [...dateFilteredGuests].sort((a, b) => {
      let aValue, bValue;

      // Get values based on column ID with fallbacks for different data structures
      switch (columnId) {
        case 'guest_name':
          aValue = a.guest_name || a.visitorName || a.name || "";
          bValue = b.guest_name || b.visitorName || b.name || "";
          break;
        case 'host_name':
          aValue = a.host_name || a.hostName || a.visitorHost || "";
          bValue = b.host_name || b.hostName || b.visitorHost || "";
          break;
        case 'facility_name':
          aValue = a.facility_name || a.facility || a.facilityName || "";
          bValue = b.facility_name || b.facility || b.facilityName || "";
          break;
        case 'check_in_time':
          aValue = a.check_in_time || a.startDate || a.checkIn || "";
          bValue = b.check_in_time || b.startDate || b.checkIn || "";
          break;
        case 'check_out_time':
          aValue = a.check_out_time || a.endDate || a.checkOut || "";
          bValue = b.check_out_time || b.endDate || b.checkOut || "";
          break;
        default:
          aValue = a[columnId] || "";
          bValue = b[columnId] || "";
      }

      // Handle null/undefined values
      if (!aValue && !bValue) return 0;
      if (!aValue) return sortDirection === 'asc' ? 1 : -1;
      if (!bValue) return sortDirection === 'asc' ? -1 : 1;

      // Handle date/time sorting
      if (columnId.includes('time') || columnId.includes('Date') || columnId.includes('date')) {
        const dateA = new Date(aValue);
        const dateB = new Date(bValue);

        // Check if dates are valid
        if (isNaN(dateA.getTime()) && isNaN(dateB.getTime())) return 0;
        if (isNaN(dateA.getTime())) return sortDirection === 'asc' ? 1 : -1;
        if (isNaN(dateB.getTime())) return sortDirection === 'asc' ? -1 : 1;

        return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
      }

      // Handle string sorting
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase().trim();
        bValue = bValue.toLowerCase().trim();
      }

      // Handle numeric sorting
      if (!isNaN(aValue) && !isNaN(bValue)) {
        return sortDirection === 'asc' ? Number(aValue) - Number(bValue) : Number(bValue) - Number(aValue);
      }

      // Default string comparison
      if (aValue < bValue) {
        return sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });

    setDateFilteredGuests(sortedData);
  };
  // console.log(selectedPatient);


  // ---------------- Date Filtering ----------------
  const filterVisitorsByDate = (date) => {
    const formattedSelectedDate = moment(date).format("M-D-YYYY");
    const filteredVisitors = allGuests.filter((visitor) => {
      const visitorDate = moment(visitor.startDate, "M-D-YYYY h:mm A").format("M-D-YYYY");
      return visitorDate === formattedSelectedDate;
    });
    setDateFilteredGuests(filteredVisitors);
    setShowVisitorForm(false)
  };
  // Refresh function to reload guests data
  const refreshGuestsData = () => {
    const fetFacilityId = localStorage.getItem("selectedFacility");
    if (fetFacilityId) {
      getVisitGuests(fetFacilityId)
        .then((response) => {
          const guests = response?.data?.data || [];
          setAllGuests(guests);
          setDateFilteredGuests(guests);
        })
        .catch(() => {
          setAllGuests([]);
          setDateFilteredGuests([]);
        });
    }
  };

  const visitorColumns = getVisitorColumns({
    openModal,
    handlePrintClick,
    profileImage: defaultImg,
    onRefresh: refreshGuestsData,
  });
  // ---------------- Derived Data ----------------
  // Apply the active filter on top of the date filtered guests.
  const displayedGuestList = (Array.isArray(dateFilteredGuests) ? dateFilteredGuests : []).filter((visitor) => {
    if (activeFilter === "all") return true;
    if (activeFilter === "recent") return visitor.recent;
    if (activeFilter === "invited") return visitor.invited;
    if (activeFilter === "checkedin") return visitor.checkedIn;
    if (activeFilter === "checkedout") return visitor.checkedOut;
    if (activeFilter === "checkInDenied") return visitor.checkInDenied;
    return true;
  });

  // Filter options for the FilterButtons component
  const filterOptions = [
    { value: "recent", label: "Recent visitors" },
    { value: "all", label: "All Visitors" },
    { value: "invited", label: "All Invited" },
    { value: "checkedin", label: "Checked In" },
    { value: "checkedout", label: "Checked Out" },
    { value: "checkInDenied", label: "Check-in-denied" }
  ];

  return (
    <div className="pl-24 mt-20 pr-8 h-full">
      {/* Title */}
      <div className="text-[24px] font-normal text-[#4F2683]">
        <h3>Reception Desk</h3>
      </div>

      {/* Top Search, Custom Date Picker, Filter Buttons & Home Icon Row */}
      <div>
        <div className="flex flex-col sm:flex-row sm:justify-center items-center  sm:gap-6 my-4 mb-8">

          <Button type="imgbtn" className="px-[10px] py-2" icon={homeicon} onClick={handleHome} />

          {/* Host Search */}
          <HostSearch
            placeholder={patientSearchPlaceholder}
            searchTerm={searchTerm}
            onInputChange={(value) => {
              setHostSearchTerm(value);
              handleHostSearchInputChange(value); // your existing logic
            }}
            results={searchResults}
            onResultClick={handleHostClick}
            isDropdownVisible={isDropdownVisible}
            containerRef={patientSearchRef}
          />

          {/* Visitor Search */}
          <VisitorSearch
            placeholder={guestSearchPlaceholder}
            searchTerm={guestSearchTerm}
            onInputChange={handleVisitorSearchInputChange}
            results={guestSearchResults}
            onResultClick={handleGuestClick}
            isDropdownVisible={isGuestDropdownVisible}
            containerRef={guestSearchRef}
            onCreateClick={handleCreateVisitorClick} // Pass the create visitor handler
          />

          {/* Custom Date Picker */}
          <DateInput
            value={selectedDate}
            onChange={(date) => {
              setSelectedDate(date);
              filterVisitorsByDate(date);
            }}
            placeholder="Select a date"
            className="w-[25%]  rounded-md text-[#4F2683] "
          />

        </div>
      </div>
      {/* Render DetailsCard if a guest is selected */}
      {selectedGuest && (
        <DetailsCard
          OpenPhotoModal={() => setIsModalOpen(true)}
          handleHistoryOpen={handleHistoryOpen}
          profileImage={selectedGuest?.image || defaultImg}
          defaultImage={defaultImg}
          name={selectedGuest?.visitorName || selectedGuest?.name || "N/A"}
          showHistoryButton={false}
          additionalFields={[
            {
              label: "Email",
              value: selectedGuest?.email || "N/A"
            },
            {
              label: "Phone",
              value: selectedGuest?.phone || "N/A"
            },
            {
              label: "Status",
              value: selectedGuest?.status || "N/A"
            },
            {
              label: "Start Date",
              value: selectedGuest?.startDate || "N/A",
            },
            {
              label: "End Date",
              value: selectedGuest?.endDate ? formatDateTime(selectedGuest.endDate) : "N/A",
            }
          ]}
        />
      )}
      {/* Render Visitor Form */}
      {showVisitorForm && (
        <VisitorForm
          onAddGuest={handleAddVisitor}
          onClose={() => setShowVisitorForm(false)}
          hostName={selectedPatient?.name || `${selectedPatient?.first_name || ''} ${selectedPatient?.last_name || ''}`.trim()} // Pass hostName to VisitorForm
          hostId={selectedPatient?.id} // Pass hostId from /master-data/identity-hub API (primary key)
          selectedHost={selectedPatient} // Pass complete host object
          escortId={null} // Can be added later when escort selection is implemented
          selectedEscort={null} // Can be added later when escort selection is implemented
        />
      )}
      {/* Show table and Add button if guest is selected */}
      {selectedGuest && (
        <>
          <FilterButtons filter={activeFilter} onFilterChange={setActiveFilter} filterOptions={filterOptions} />
          <div className="mt-4">
            <GenericTable
              title={"Guests"}
              searchTerm={tableSearchTerm}
              onSearchChange={(e) => setTableSearchTerm(e.target.value)}
              onSort={handleSort}
              columns={visitorColumns}
              data={displayedGuestList}
              fixedHeader
              onAdd={() => setShowVisitorForm(true)}
              fixedHeaderScrollHeight="300px"
              highlightOnHover
              showAddButton={true}
              striped={false}
            />
          </div>
        </>
      )}
      {/* If no guest is selected, show table as before for selectedPatient */}
      {!selectedGuest && (
        <>
          <FilterButtons filter={activeFilter} onFilterChange={setActiveFilter} filterOptions={filterOptions} />
          <div className="mt-4">
            <GenericTable
              title={"Guests"}
              searchTerm={tableSearchTerm}
              onSearchChange={(e) => setTableSearchTerm(e.target.value)}
              onSort={handleSort}
              columns={visitorColumns}
              data={displayedGuestList}
              fixedHeader
              onAdd={() => setShowVisitorForm(true)}
              fixedHeaderScrollHeight="300px"
              highlightOnHover
              showAddButton={selectedPatient ? true : false}
              striped={false}
            />
          </div>
        </>
      )}

      {/* Print Modal */}
      {printModalVisible && selectedGuest && (
        <PrintModal guest={selectedGuest} onClose={handleClosePrintModal} />
      )}

      {/* Edit Photo Modal */}
      {isModalOpen && (
        <EditPhotoModal onClose={() => setIsModalOpen(false)} onSave={handleImageCaptured} />
      )}

      {/* Create Visitor Modal */}
      {isCreateVisitorModalOpen && (
        <CreateVisitorModal
          isOpen={isCreateVisitorModalOpen}
          onClose={() => setIsCreateVisitorModalOpen(false)}
          onSubmit={handleCreateVisitorSubmit}
        />
      )}
    </div>
  );
};

export default ReceptionDesk;
